import os
import pandas as pd
from concurrent.futures import ThreadPoolExecutor
from utility import get_sell_option_price, get_calender_diff_value, find_closest_option
from VIX_Threshold import get_vix
#import logger_config
import time
from datetime import datetime


#logger = logger_config.setup_logger('main_logger', 'main.log')

sides = {
    "CE_SELL": "SELL",
    "PE_SELL": "SELL",
    "CE_BUY": "BUY",
    "PE_BUY": "BUY"
}

def thursday_execute_BCS(mainkey, subfolders, start_time, exit_time, folder_date_dt,stop_loss, target_profit, ratio_check, resize_factor):
       
    target_date = os.path.basename(mainkey)
    #logger.info(f"Target Date: {target_date}")
    
    vix_close_value = get_vix(target_date,start_time)
    if vix_close_value is None:
        #logger.info(f"Skipping processing for {target_date} due to missing VIX data.")
        return None

    sell_option_price = get_sell_option_price(vix_close_value)
    #logger.info(f"sell_option_price: {sell_option_price}")
    calender_option_price = get_calender_diff_value(sell_option_price, vix_close_value)
    #logger.info(f"calender_option_price: {calender_option_price}")

    resize = resize_factor
    sell_option_price *= resize
    calender_option_price *= resize

   
   
    results = {}

    def process_single_file(filename, df, subfolder, side, target_price):
        """Process a single file and return the best option found in that file"""
        try:
            # Add filename as a column to track which file each row came from
            df_copy = df.copy()
            df_copy['source_file'] = filename
            
            # Find closest option in this specific file
            result = find_closest_option(df_copy, target_date, target_price, start_time, filename)
            
            if result is None:
                return None
                
            symbol, row = result
            
            if row is not None:
                return {
                    "subfolder": subfolder,
                    "filename": filename,
                    "symbol": symbol,
                    "data": {
                        "Date": int(row["YMD"]),
                        "Time": row["Time"],
                        "symbol": symbol,
                        "ltp": float(row["Close"]),
                        "side": side
                    },
                    "price_diff": abs(float(row["Close"]) - target_price)  # For finding best option
                }
            
            return None
            
        except Exception as e:
            #logger.exception(f"Error in process_single_file for {filename} in {subfolder}: {e}")
            return None

    def find_best_option_from_file_results(file_results, target_price):
        """Find the best option from all file results for a subfolder"""
        if not file_results:
            return None
            
        # Find the result with minimum price difference
        best_result = min(file_results, key=lambda x: x["price_diff"])
        return best_result["data"]

    # 🔧 Use all CPU cores for maximum parallelization
    cpu_count = os.cpu_count()
    max_workers = cpu_count  # Use all cores instead of cpu_count - 1
    #logger.info(f"Using {max_workers} threads for processing individual files.")
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        
        # Submit individual file processing tasks
        for subfolder, side in sides.items():
            if subfolder not in subfolders:
                #logger.warning(f"{subfolder} not found in subfolders dict.")
                continue
                
            subfolder_data = subfolders[subfolder]
            if not subfolder_data:
                #logger.warning(f"No data found for {subfolder}")
                continue
                
            price = sell_option_price if side == "SELL" else calender_option_price
            
            # Submit each file as a separate task for parallel processing
            for filename, df in subfolder_data.items():
                future = executor.submit(process_single_file, filename, df, subfolder, side, price)
                futures.append((future, subfolder))
        
        # Collect results and group by subfolder
        subfolder_file_results = {}
        for future, subfolder in futures:
            try:
                result = future.result()
                if result:
                    if subfolder not in subfolder_file_results:
                        subfolder_file_results[subfolder] = []
                    subfolder_file_results[subfolder].append(result)
            except Exception as e:
                #logger.exception(f"Exception in file processing thread for {subfolder}: {e}")
                continue
        
        # Find the best option for each subfolder from all its file results
        for subfolder, file_results in subfolder_file_results.items():
            price = sell_option_price if sides[subfolder] == "SELL" else calender_option_price
            best_option = find_best_option_from_file_results(file_results, price)
            if best_option:
                results[subfolder] = best_option
            
    return results, vix_close_value
    

    
     
